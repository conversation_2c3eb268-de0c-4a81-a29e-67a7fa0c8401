#include "AStar.h"


// 内存分配宏
#define CHECK_MALLOC(ptr, size) \
    do { \
        ptr = malloc(size); \
        if (ptr == NULL) { \
            return -6; \
        } \
        memset(ptr, 0, size); \
    } while (0)

// 检查点是否在地图范围内
#define IS_POINT_IN_MAP(As, point) \
    (point.X >= 0 && point.X < As->MapWidth && point.Y >= 0 && point.Y < As->MapHeight)

// 检查索引是否在地图节点范围内
#define IS_INDEX_IN_MAP_NODE(As, index) \
    (index >= 0 && index < As->MapWidth * As->MapHeight)

// 计算曼哈顿距离的宏定义
#define MANHATTAN_DISTANCE(x1, y1, x2, y2) \
    (abs(x1 - x2) + abs(y1 - y2))

AStar* AStar_New(char* mapdata, int len, int code) {
	if (len < 8) {

		return 0;
	}
	short W = *(short*)mapdata;
	short H = *(short*)(mapdata + 2);
	int bt = *(int*)(mapdata + 4);
	if (bt != 1)
	{
		// 处理特殊地图格式
		if (H == 0 && W * bt == len - 8) {
			H = bt;
			bt = 2;
		}
	}
	if (W <= 0 || H <= 0 || W * H != len - 8)
	{

		return NULL;
	}
	if (bt == 1) // 标准地图格式
	{
		AStar* As = malloc(sizeof(AStar));
		memset(As, 0, sizeof(AStar));
		As->code = code;
		As->MapWidth = W;
		As->MapHeight = H;

		if (AStar_LoadMapData(As, mapdata + 8, len - 8) == 0)
		{
			free(As);
			As = NULL;
			return NULL;
		}
		return As;
	}
	else if (bt == 2) // 扩展地图格式
	{
		int cMapDataLen;
		char* cMapData = AStar_EMapToCMap(mapdata, len, &cMapDataLen);
		if (cMapData != NULL)
		{
			W = *(short*)cMapData;
			H = *(short*)(cMapData + 2);
			bt = *(int*)(cMapData + 4);

			if (W <= 0 || H <= 0 || W * H != cMapDataLen - 8)
			{
				free(cMapData);
				return NULL;
			}

			AStar* As = malloc(sizeof(AStar));
			memset(As, 0, sizeof(AStar));
			As->code = code;
			As->MapWidth = W;
			As->MapHeight = H;
			if (AStar_LoadMapData(As, cMapData + 8, cMapDataLen - 8) == 0)
			{
				free(As);
				free(cMapData);
				As = NULL;
				return NULL;
			}
			free(cMapData);
			return As;
		}
	}
	return NULL;
}
// 优化的AStar_LoadMapData函数，统一内存分配
int AStar_LoadMapData(AStar* As, char* mapdata, int len) {
	// 一次性分配所有需要的内存，避免内存碎片
	// 增加距离图和墙体点列表的内存
	size_t totalSize = len + len * sizeof(AStarNode) + (len / 2) * sizeof(AStarHeapNode) +
		len * sizeof(unsigned short) + len * sizeof(int);
	char* memory = malloc(totalSize);

	if (!memory) return 0;

	// 设置指针
	As->MapData = memory;
	As->MapNode = (AStarNode*)(memory + len);
	As->OpenList = (AStarHeapNode*)(memory + len + len * sizeof(AStarNode));
	As->OpenListIndexMap = (int*)(memory + len + len * sizeof(AStarNode) + (len / 2) * sizeof(AStarHeapNode));
	As->distanceMap = (unsigned short*)(memory + len + len * sizeof(AStarNode) + (len / 2) * sizeof(AStarHeapNode) + len * sizeof(int));

	// 复制数据
	memcpy(As->MapData, mapdata, len);
	memset(As->MapNode, 0, len * sizeof(AStarNode));
	memset(As->OpenList, 0, (len / 2) * sizeof(AStarHeapNode));
	memset(As->OpenListIndexMap, -1, len * sizeof(int));
	memset(As->distanceMap, 0, len * sizeof(unsigned short));

	// 初始化计数器和标志
	As->OpenListCount = 0;
	As->distanceMapValid = 0;
	As->wallPoints = NULL;
	As->wallPointCount = 0;
	As->maxWallPoints = 0;

	// 记录内存块起始地址，便于释放
	As->memoryBlock = memory;

	return 1;
}

// 优化的AStar_Free函数
void AStar_Free(AStar* As) {
	if (As != NULL) {
		if (As->memoryBlock != NULL) {
			free(As->memoryBlock);
			As->memoryBlock = NULL;
			As->MapData = NULL;
			As->MapNode = NULL;
			As->OpenList = NULL;
			As->OpenListIndexMap = NULL;
			As->distanceMap = NULL;
		}
		if (As->wallPoints != NULL) {
			free(As->wallPoints);
			As->wallPoints = NULL;
		}
		free(As);
	}
}
char* AStar_EMapToCMap(char* mapdata, int len, int* retLen) {
	int EW = *(int*)mapdata;
	int EH = *(int*)(mapdata + 4);
	if (len != EW * EH + 8)
	{
		return 0;
	}
	int NewH = EH + 1;
	int NewW = EW + 1;
	char* newMapWidthData = malloc(NewW);
	memset(newMapWidthData, 0xff, NewW);
	char* retMapdata = malloc((NewW) * (NewH)+8);
	*retLen = (NewW) * (NewH)+8;
	memset(retMapdata, 0, *retLen);
	memcpy(retMapdata, &NewW, 2);
	memcpy(retMapdata + 2, &NewH, 2);
	retMapdata[4] = 1;
	memcpy(retMapdata + 8, newMapWidthData, NewW);
	for (size_t i = 0; i < EH; i++)
	{
		memcpy(newMapWidthData + 1, mapdata + (i * EW) + 8, EW);
		memcpy(retMapdata + (i + 1) * NewW + 8, newMapWidthData, NewW);
	}
	free(newMapWidthData);
	return retMapdata;
}
void AStar_SaveMapFile(AStar* As, const char* path) {
	if (As != NULL)
	{
		int dataLen = (As->MapWidth) * (As->MapHeight) + 8;
		if (dataLen > 8)
		{
			char* Mapdata = malloc(dataLen);
			memset(Mapdata, 0, dataLen);
			memcpy(Mapdata, &As->MapWidth, 2);
			memcpy(Mapdata + 2, &As->MapHeight, 2);
			Mapdata[4] = 1;
			memcpy(Mapdata + 8, As->MapData, dataLen - 8);
			FILE* fp;
			fp = fopen(path, "wb");
			if (fp != NULL)
			{
				fwrite(Mapdata, dataLen, 1, fp);
				fclose(fp);
			}
			free(Mapdata);
		}
	}

}
// ===================== 优化的OpenList堆管理 =====================
// 交换堆节点
void SwapHeapNode(AStarHeapNode* a, AStarHeapNode* b) {
	AStarHeapNode tmp = *a;
	*a = *b;
	*b = tmp;
}

// 向上调整
void HeapifyUp(AStarHeapNode* heap, int idx) {
	while (idx > 0) {
		int parent = (idx - 1) / 2;
		if (heap[parent].F > heap[idx].F) {
			SwapHeapNode(&heap[parent], &heap[idx]);
			idx = parent;
		}
		else {
			break;
		}
	}
}

// 向下调整
void HeapifyDown(AStarHeapNode* heap, int count) {
	int idx = 0;
	AStarHeapNode temp = heap[idx]; // 保存要调整的节点

	while (1) {
		int left = 2 * idx + 1;
		int right = 2 * idx + 2;
		if (left >= count) break; // 没有子节点

		int smallest = (right < count&& heap[right].F < heap[left].F) ? right : left;

		if (heap[smallest].F >= temp.F) break; // 已经满足堆性质

		heap[idx] = heap[smallest]; // 将较小的子节点上移
		idx = smallest;
	}

	heap[idx] = temp; // 将原来的节点放到最终位置
}

// 插入堆节点
void HeapInsert(AStar* As, int index, int F) {
	if (As->OpenListCount >= As->MapWidth * As->MapHeight / 2) return;
	As->OpenList[As->OpenListCount].F = F;
	As->OpenList[As->OpenListCount].index = index;
	HeapifyUp(As->OpenList, As->OpenListCount);
	As->OpenListCount++;
}

// 删除堆顶节点
void HeapPop(AStar* As) {
	if (As->OpenListCount == 0) return;
	As->OpenList[0] = As->OpenList[As->OpenListCount - 1];
	As->OpenListCount--;
	HeapifyDown(As->OpenList, As->OpenListCount);
}
// 更新堆中某个节点的F值并重新堆化
void HeapUpdate(AStar* As, int idx, int newF) {
	As->OpenList[idx].F = newF;
	HeapifyUp(As->OpenList, idx);
	HeapifyDown(As->OpenList, As->OpenListCount);
}
// ===================== 对外接口OpenList管理 =====================
// 对外接口AStar_AddHeapNode
void AStar_AddHeapNode(AStar* As, int index, int F) {
	HeapInsert(As, index, F);
}
// 对外接口AStar_DelHeapNode
void AStar_DelHeapNode(AStar* As) {
	HeapPop(As);
}
// 对外接口AStar_ComparisonHeapNode
void AStar_ComparisonHeapNode(AStar* As, int idx) {
	HeapifyUp(As->OpenList, idx);
	HeapifyDown(As->OpenList, As->OpenListCount);
}
AStarPoint AStar_GetPrevPoint(AStar* As, AStarPoint Point) {
	int i = As->MapWidth * Point.Y + Point.X;
	return As->MapNode[i].ParentNode;
}

// 获取寻路失败时最接近目标的可达点
AStarPoint AStar_GetClosestReachablePoint(AStar* As) {
	int MapNodeLen = As->MapWidth * As->MapHeight;
	int bestIndex = -1;
	int minH = INT_MAX;

	// 在已探索的节点中找到H值最小的点（最接近目标）
	for (int i = 0; i < MapNodeLen; i++) {
		if (As->MapNode[i].IsRun && As->MapNode[i].H < minH) {
			minH = As->MapNode[i].H;
			bestIndex = i;
		}
	}

	AStarPoint result = {-1, -1}; // 默认返回无效点
	if (bestIndex != -1) {
		result.X = bestIndex % As->MapWidth;
		result.Y = bestIndex / As->MapWidth;
	}

	return result;
}
int AStar_GetCode(AStar* As) {
	return As->code;
}
char AStar_GetPointVal(AStar* As, AStarPoint Point) {
	int index = As->MapWidth * Point.Y + Point.X;
	return As->MapData[index];
}
void AStar_SetPointVal(AStar* As, AStarPoint Point, char val) {
	int index = As->MapWidth * Point.Y + Point.X;
	As->MapData[index] = val;
}
int AStar_CreateMapFile(int MapWidth, int MapHeight, AStarPoint* losePoint, int losePointlen, const char* path) {
	if (losePointlen < 0)return 0;
	FILE* fp;
	fp = fopen(path, "wb");
	if (fp == NULL) return 0;
	int dataLen = (MapWidth) * (MapHeight)+8;
	unsigned char* Mapdata = malloc(dataLen);
	memset(Mapdata, 0, dataLen);
	memcpy(Mapdata, &MapWidth, 2);
	memcpy(Mapdata + 2, &MapHeight, 2);
	Mapdata[4] = 1;
	for (size_t i = 0; i < losePointlen; i++)
	{
		int index = (MapWidth * losePoint[i].Y + losePoint[i].X) + 8;
		Mapdata[index] = 255;
	}
	fwrite(Mapdata, dataLen, 1, fp);
	fclose(fp);
	free(Mapdata);
	return 1;
}
AStar* AStar_CreateMapFileInBMP(const unsigned char* bmpData, int bmpDataLen, const char* path, int code) {

	FILE* fb = NULL;

	// 参数验证
	if (bmpData == NULL || bmpDataLen < sizeof(BMPFILEHEADER) + sizeof(BMPINFOHADER)) {
		return NULL;
	}

	// 如果提供了path参数，则同时写入文件
	if (path != NULL) {
		fb = fopen(path, "wb");//写入
		if (fb == NULL) {
			return NULL;
		}
	}

	// 直接从内存中读取BMP头信息
	const unsigned char* dataPtr = bmpData;
	BMPFILEHEADER bmpHead;
	BMPINFOHADER bmpinfo;

	// 复制BMP文件头
	memcpy(&bmpHead, dataPtr, sizeof(BMPFILEHEADER));
	dataPtr += sizeof(BMPFILEHEADER);

	// 复制BMP信息头
	memcpy(&bmpinfo, dataPtr, sizeof(BMPINFOHADER));
	dataPtr += sizeof(BMPINFOHADER);

	// 验证BMP格式
	if (bmpHead.bfType != 0x4d42) {
		if (fb) fclose(fb);
		return NULL;
	}
	if (bmpinfo.biBitCount != 24) {
		if (fb) fclose(fb);
		return NULL;
	}

	int size = bmpinfo.biImageSize;
	int Width = bmpinfo.biWidth;
	int Height = bmpinfo.biHeight;

	// 验证数据长度是否足够
	if (bmpDataLen < sizeof(BMPFILEHEADER) + sizeof(BMPINFOHADER) + size) {
		if (fb) fclose(fb);
		return NULL;
	}

	// 直接使用内存中的位图数据，无需额外分配
	const unsigned char* bits = dataPtr;

	unsigned char* MapData = malloc(Width * Height + 8);
	if (MapData == NULL) {
		if (fb) fclose(fb);
		return NULL;
	}

	memset(MapData, 0xFF, Width * Height + 8);
	memcpy(MapData, &Width, 2);
	memcpy(MapData + 2, &Height, 2);
	*(int*)(MapData + 4) = 1;
	int line = (Width * 24 / 8 + 3) / 4 * 4; // 计算行字节数

	for (size_t X = 0; X < Width; X++)
	{
		for (size_t Y = 0; Y < Height; Y++)
		{

			int mapy = Height - Y - 1; // BMP图像Y轴是倒置的
			int index = (mapy * line) + X * 3;
			if (bits[index] == 255 && bits[index + 1] == 255 && bits[index + 2] == 255)
			{
				int mapindex = Width * Y + X + 8;
				MapData[mapindex] = 0;
			}

		}
	}

	// 如果提供了path参数，写入文件
	if (fb != NULL) {
		fwrite(MapData, Width * Height + 8, 1, fb);
		fclose(fb);
	}

	// 直接创建AStar对象
	AStar* As = AStar_New((char*)MapData, Width * Height + 8, code);
	free(MapData);

	return As;
}
void AStar_SaveBMP(AStar* As, const char* path) {

	FILE* fp;
	fp = fopen(path, "wb");
	if (fp == NULL) return;
	int biBitCount = 24;
	BMPFILEHEADER head;
	int line = (As->MapWidth * biBitCount / 8 + 3) / 4 * 4; // 计算行字节数
	int size = line * As->MapHeight;
	head.bfType = 0x4d42;
	head.bfReserved1 = 0;
	head.bfReserved2 = 0;
	head.bfSize = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHADER) + size;
	head.bfOffset = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHADER);
	BMPINFOHADER info = { 0 };
	info.biInfoSize = sizeof(BMPINFOHADER);
	info.biHeight = As->MapHeight;
	info.biWidth = As->MapWidth;
	info.biPlanes = 1;
	info.biBitCount = biBitCount;
	info.biImageSize = size;
	info.biCompression = 0;
	info.biXPelsPerMeter = 3780;
	info.biYPelsPerMeter = 3780;
	unsigned char* bits = malloc(size);
	memset(bits, 0x0, size);
	for (size_t X = 0; X < As->MapWidth; X++)
	{
		for (size_t Y = 0; Y < As->MapHeight; Y++)
		{
			int mapindex = As->MapWidth * Y + X;
			if (As->MapData[mapindex] == 0)
			{
				int mapy = As->MapHeight - Y - 1; // BMP图像Y轴是倒置的
				int index = (mapy * line) + X * 3;
				bits[index] = 255;//B
				bits[index + 1] = 255;//G
				bits[index + 2] = 255;//R
			}
		}
	}
	fwrite(&head, sizeof(BMPFILEHEADER), 1, fp);
	fwrite(&info, sizeof(BMPINFOHADER), 1, fp);
	fwrite(bits, size, 1, fp);
	fclose(fp);
	free(bits);
}
int AStar_FindPath(AStar* As, AStarPoint Start, AStarPoint End, int dir, AStarPoint* losePoint, int losePointlen, int LoseStartErr) {
	/*if (Start.X == End.X && Start.Y == End.Y) return -1;
	if (Start.X >= As->MapWidth || Start.Y >= As->MapHeight || Start.X < 0 || Start.Y < 0) return -2;
	if (End.X >= As->MapWidth || End.Y >= As->MapHeight || End.X < 0 || End.Y < 0) return -3;*/

	// 如果物品靠近墙体，还需要进行优化
	//	1.寻路失败返回路径
	//	2.判断坐标.x+1 是否黑色 如果黑色采取相应措施
	//	3.判断物品往后一个模型是否为白色，如果为白色移动区域挑战
	// 
	// 参数验证（使用优化的宏）
	if (Start.X == End.X && Start.Y == End.Y) return -1;
	if (!IS_POINT_IN_MAP(As, Start)) return -2;
	if (!IS_POINT_IN_MAP(As, End)) return -3;

	int index = getMapIndex(As, End.X, End.Y);
	if (As->MapData[index] == 255)return -4;
	index = getMapIndex(As, Start.X, Start.Y);
	if (!LoseStartErr && As->MapData[index] == 255)return -5;

	if (dir != 4)dir = 8;
	int MapNodeLen = As->MapWidth * As->MapHeight;
	AStarHeapNode* losePointNode;

	if (losePointlen > 0)
	{
		losePointNode = malloc(losePointlen * sizeof(AStarHeapNode));
		if (losePointNode == NULL) {
			return -6; // 内存分配失败
		}
		memset(losePointNode, 0, losePointlen * sizeof(AStarHeapNode));
		int loseerr = -1;
		for (size_t i = 0; i < losePointlen; i++)
		{
			int Nodeindex = getMapIndex(As, losePoint[i].X, losePoint[i].Y);
			if (!IS_POINT_IN_MAP(As, losePoint[i]) || !IS_INDEX_IN_MAP_NODE(As, Nodeindex)) { loseerr = i; break; }
			losePointNode[i].F = As->MapData[Nodeindex];
			losePointNode[i].index = Nodeindex;
			As->MapData[Nodeindex] = 255;
		}
		if (loseerr > -1)
		{
			for (size_t i = 0; i < loseerr; i++)
			{
				As->MapData[losePointNode[i].index] = losePointNode[i].F;
			}
			free(losePointNode);
			return -6;
		}
	}
	// 初始化搜索状态
	for (size_t i = 0; i < MapNodeLen; i++)
	{
		As->MapNode[i].IsRun = 0;
	}
	As->OpenListCount = 0; // 清空开放列表
	index = As->MapWidth * Start.Y + Start.X; // 起始点
	As->MapNode[index].IsRun = 1;
	As->MapNode[index].G = 0;
	As->MapNode[index].H = calcH(Start, End);//(abs(Start.X - End.X) + abs(Start.Y - End.Y)) * 10;
	As->MapNode[index].F = As->MapNode[index].G + As->MapNode[index].H;
	AStar_AddHeapNode(As, index, As->MapNode[index].F);

	AStarPoint FPoint = { 0,0 };
	AStarPoint ZPoint = { 0,0 };
	static const int Xdir[8] = { -1, 1, 0, 0, -1, 1, 1, -1 }; // 左 右 上 下 左上 右上 右下 左下
	static const int Ydir[8] = { 0, 0, -1, 1, -1, -1, 1, 1 };
	int Isok = 0;
	AStarNode TmpNode;

	while (1)
	{
		index = As->OpenList[0].index;
		AStar_DelHeapNode(As); // 删除堆顶节点
		int F_G = As->MapNode[index].G;
		FPoint.X = index % As->MapWidth;
		FPoint.Y = index / As->MapWidth;
		// 获取当前点的前一个点（用于计算转向）
		AStarPoint prevPoint = As->MapNode[index].ParentNode;
		int prev_dx = 0, prev_dy = 0;
		int has_prev = (FPoint.X != Start.X || FPoint.Y != Start.Y);

		if (has_prev) {
			prev_dx = FPoint.X - prevPoint.X;
			prev_dy = FPoint.Y - prevPoint.Y;
		}
		for (size_t i = 0; i < dir; i++)
		{
			ZPoint.X = FPoint.X + Xdir[i];
			ZPoint.Y = FPoint.Y + Ydir[i];

			if (!IS_POINT_IN_MAP(As, ZPoint) || CheckModelCollision(As, ZPoint)) {//
				continue; // 跳过不可通行的点
			}

			index = getMapIndex(As, ZPoint.X, ZPoint.Y);
			if (As->MapData[index] == 255) { continue; }

			// 使用优化的G代价计算函数
			TmpNode.G = CalculateNodeGCost(As, F_G, End, FPoint, ZPoint, i, has_prev, prev_dx, prev_dy);
			TmpNode.IsRun = 1;
			TmpNode.H = calcH(ZPoint, End);
			TmpNode.F = TmpNode.G + TmpNode.H;
			TmpNode.ParentNode.X = FPoint.X;
			TmpNode.ParentNode.Y = FPoint.Y;
			if (As->MapNode[index].IsRun)
			{
				if (As->MapNode[index].F > TmpNode.F)
				{
					As->MapNode[index] = TmpNode;
					for (size_t j = 0; j < As->OpenListCount; j++)
					{
						if (As->OpenList[j].index == index)
						{
							As->OpenList[j].F = TmpNode.F;
							AStar_ComparisonHeapNode(As, j);
							break;
						}
					}
				}
			}
			else
			{
				As->MapNode[index] = TmpNode;
				AStar_AddHeapNode(As, index, TmpNode.F);
			}
			if (ZPoint.X == End.X && ZPoint.Y == End.Y)
			{
				Isok = 1;
				break;
			}
		}
		if (Isok)
		{
			break;
		}
		if (As->OpenListCount == 0) {
			// 寻路失败时，找到最接近目标的点并保留路径信息
			int bestIndex = -1;
			int minH = INT_MAX;

			// 在已探索的节点中找到H值最小的点（最接近目标）
			for (int i = 0; i < MapNodeLen; i++) {
				if (As->MapNode[i].IsRun && As->MapNode[i].H < minH) {
					minH = As->MapNode[i].H;
					bestIndex = i;
				}
			}

			// 如果找到了最接近的点，保留其路径信息
			if (bestIndex != -1) {
				// 清除其他节点的运行状态，但保留从起点到最佳点的路径链
				AStarPoint currentPoint;
				currentPoint.X = bestIndex % As->MapWidth;
				currentPoint.Y = bestIndex / As->MapWidth;

				// 标记从最佳点到起点的路径链，保持这些节点的IsRun状态
				while (!(currentPoint.X == Start.X && currentPoint.Y == Start.Y)) {
					int currentIndex = As->MapWidth * currentPoint.Y + currentPoint.X;
					// 保持路径节点的IsRun状态为1，其他节点清零
					currentPoint = As->MapNode[currentIndex].ParentNode;
				}
			}

			if (losePointlen > 0)
			{
				for (size_t i = 0; i < losePointlen; i++)
				{
					As->MapData[losePointNode[i].index] = losePointNode[i].F;
				}
				free(losePointNode);
			}

			return -8; // 部分路径可用（寻路失败但保留了最接近目标的路径）
		}
	}
	if (losePointlen > 0)
	{
		for (size_t i = 0; i < losePointlen; i++)
		{
			As->MapData[losePointNode[i].index] = losePointNode[i].F;
		}
		free(losePointNode);
	}

	//SmoothPath(As, path, pathLen, smoothPath, &smoothLen);
	return 0;
}

// 计算启发式函数H值
int calcH(AStarPoint point, AStarPoint end) {
	// 使用曼哈顿距离作为启发式函数，适用于四方向和八方向移动
	// 乘以10是为了与对角线移动代价14保持一致的精度
	return (abs((double)(end.X - point.X)) + abs((double)(end.Y - point.Y))) * 10;
}
int getMapIndex(AStar* As, int x, int y) {
	return As->MapWidth * y + x;
}

// 检查两点间直线是否畅通
int IsLineClear(AStar* As, AStarPoint start, AStarPoint end) {
	int dx = abs(end.X - start.X), dy = abs(end.Y - start.Y);
	int sx = (start.X < end.X) ? 1 : -1;
	int sy = (start.Y < end.Y) ? 1 : -1;
	int err = dx - dy;
	int x = start.X, y = start.Y;
	while (x != end.X || y != end.Y) {
		if (CheckModelCollision(As, (AStarPoint) { x, y })) return 0;
		int e2 = 2 * err;
		if (e2 > -dy) { err -= dy; x += sx; }
		if (e2 < dx) { err += dx; y += sy; }
	}
	return 1;
}
// 模型碰撞检测
int CheckModelCollision(AStar* As, AStarPoint center) {
	// 检查中心点是否在地图内
	if (!IS_POINT_IN_MAP(As, center))
		return 1; // 超出地图边界

	// 检查模型的关键点是否碰撞
	// 考虑40x10模型尺寸，检查8个关键点
	int checkPoints[8][2] = {
		{-HALF_WIDTH, 0}, {HALF_WIDTH, 0},  // 左右中点
		{0, -HALF_HEIGHT}, {0, HALF_HEIGHT},  // 上下中点
		{-HALF_WIDTH, -HALF_HEIGHT}, {HALF_WIDTH, -HALF_HEIGHT},  // 上左上右
		{-HALF_WIDTH, HALF_HEIGHT}, {HALF_WIDTH, HALF_HEIGHT}  // 下左下右
	};

	for (int i = 0; i < 8; i++) {
		int x = center.X + checkPoints[i][0];
		int y = center.Y + checkPoints[i][1];

		if (x >= 0 && x < As->MapWidth && y >= 0 && y < As->MapHeight) {
			if (As->MapData[y * As->MapWidth + x] == 255)
				return 1;
		}
		else {
			return 1; // 超出地图边界
		}
	}

	return 0;
}

// 计算两个方向的夹角
int GetDirectionChange(int dx1, int dy1, int dx2, int dy2) {
	// 使用向量点积计算两个方向的夹角
	double dot = dx1 * dx2 + dy1 * dy2;
	double len1 = sqrt(dx1 * dx1 + dy1 * dy1);
	double len2 = sqrt(dx2 * dx2 + dy2 * dy2);

	if (len1 < 0.0001 || len2 < 0.0001) return 0;

	double cosAngle = dot / (len1 * len2);
	// 计算角度并转换为度数(0-180度)
	double angle = acos(cosAngle) * 180.0 / 3.14159265358979323846;

	return (int)angle;
}

// 快速计算节点G代价（优化版本）
int CalculateNodeGCost(AStar* As, int baseG, AStarPoint End, AStarPoint FPoint, AStarPoint ZPoint,
	int dirIndex, int has_prev, int prev_dx, int prev_dy) {

	// 预定义方向数组（避免重复计算）
	static const int Xdir[8] = { -1, 1, 0, 0, -1, 1, 1, -1 };
	static const int Ydir[8] = { 0, 0, -1, 1, -1, -1, 1, 1 };

	// 预定义避墙惩罚表（查表法，避免多重if判断）
	static const int wallPenalty[10] = { 0, 300, 200, 100, 50, 20, 15, 10, 5, 0 };

	int mapIndex = As->MapWidth * ZPoint.Y + ZPoint.X;

	// 基础移动代价：直线10，对角线14
	int moveCost = (dirIndex >= 4) ? 14 : 10;
	int totalCost = baseG + moveCost + As->MapData[mapIndex];

	// 1. 方向一致性检查（快速点积计算）
	int targetDirX = End.X - FPoint.X;
	int targetDirY = End.Y - FPoint.Y;
	int currDirX = Xdir[dirIndex];
	int currDirY = Ydir[dirIndex];

	// 如果偏离目标方向（点积<0），增加惩罚
	if ((targetDirX * currDirX + targetDirY * currDirY) < 0) {
		totalCost += 5;
	}

	// 2. 转向惩罚（仅在有前一个点时计算）
	if (has_prev) {
		int curr_dx = ZPoint.X - FPoint.X;
		int curr_dy = ZPoint.Y - FPoint.Y;

		// 快速角度计算（避免浮点运算）
		// 使用向量叉积和点积的组合来快速判断角度范围
		int dot = prev_dx * curr_dx + prev_dy * curr_dy;
		//int cross = abs(prev_dx * curr_dy - prev_dy * curr_dx);
		int cross = GetDirectionChange(prev_dx, prev_dy, curr_dx, curr_dy);

		// 快速角度分类（避免调用acos函数）
		if (dot <= 0) {
			// 90度以上转弯
			if (dot < -cross) {
				// 接近180度转弯
				totalCost += 420; // 150 + 90*3
			}
			else {
				// 90-135度转弯
				totalCost += 200; // 150 + 45*2 的平均值
			}
		}
		else if (cross > dot) {
			// 45-90度转弯
			totalCost += 95; // 50 + 45*2 的平均值
		}
		else if (cross > (dot >> 2)) {
			// 10-45度转弯（使用位移代替除法）
			totalCost += 25; // 平均惩罚
		}
		// 10度以下不惩罚
	}

	// 3. 动态避墙（使用查表法优化）
	int minDist = GetMinWallDistance_Sampled(As, ZPoint);
	if (minDist <= 8) {
		totalCost += wallPenalty[minDist];
	}

	return totalCost;
}

// 获取模型到最近墙体的距离（优化版本）
int GetMinWallDistance(AStar* As, AStarPoint point) {
	// 计算模型边界
	int startX = point.X - HALF_WIDTH - 1;
	int endX = point.X + HALF_WIDTH + 1;
	int startY = point.Y - HALF_HEIGHT - 1;
	int endY = point.Y + HALF_HEIGHT + 1;

	// 逐层检查距离1到8格
	for (int dist = 1; dist <= 8; dist++) {
		// 检查左右边界的墙体
		for (int y = startY; y <= endY; y += 2) {
			if (y < 0 || y >= As->MapHeight) continue;
			int rowBase = y * As->MapWidth;

			// 检查左侧
			int lx = startX - dist;
			if (lx >= 0 && As->MapData[rowBase + lx] == 255)
				return dist;

			// 检查右侧
			int rx = endX + dist;
			if (rx < As->MapWidth && As->MapData[rowBase + rx] == 255)
				return dist;
		}

		// 检查上下边界的墙体
		for (int x = startX; x <= endX; x += 5) {
			if (x < 0 || x >= As->MapWidth) continue;

			// 检查上侧
			int uy = startY - dist;
			if (uy >= 0 && As->MapData[uy * As->MapWidth + x] == 255)
				return dist;

			// 检查下侧
			int dy = endY + dist;
			if (dy < As->MapHeight && As->MapData[dy * As->MapWidth + x] == 255)
				return dist;
		}
	}
	return 9; // 返回9表示距离墙体超过8格
}

#define ARE_POINTS_EQUAL(p1, p2) ((p1.X == p2.X) && (p1.Y == p2.Y))


// ===================== 优化的距离计算函数 =====================
// 方案2: 采样优化版本 - 降低检查精度提升速度
int GetMinWallDistance_Sampled(AStar* As, AStarPoint point) {
	// 计算模型边界
	int startX = point.X - HALF_WIDTH - 1;
	int endX = point.X + HALF_WIDTH + 1;
	int startY = point.Y - HALF_HEIGHT - 1;
	int endY = point.Y + HALF_HEIGHT + 1;

	// 只检查关键距离：1, 2, 4, 8（二进制采样）
	static const int checkDistances[] = { 1, 2,3, 4,5,6,7, 8 };
	static const int numChecks = sizeof(checkDistances) / sizeof(checkDistances[0]);

	for (int i = 0; i < numChecks; i++) {
		int dist = checkDistances[i];

		// 简化检查：只检查4个方向的关键点
		int checkPoints[4][2] = {
			{startX - dist, point.Y},  // 左
			{endX + dist, point.Y},    // 右
			{point.X, startY - dist},  // 上
			{point.X, endY + dist}     // 下
		};

		for (int j = 0; j < 4; j++) {
			int x = checkPoints[j][0];
			int y = checkPoints[j][1];

			if (x >= 0 && x < As->MapWidth && y >= 0 && y < As->MapHeight) {
				if (As->MapData[y * As->MapWidth + x] == 255) {
					return dist;
				}
			}
		}
	}

	return 9;
}
