#ifndef  ASTAR_H
#define ASTAR_H
#include <stdlib.h>
# include <string.h>
#include <stdio.h>
typedef unsigned char BYTE;         // ????BYTE????????????
typedef unsigned short WORD;     // ????WORD?????????????
typedef unsigned int DWORD;      // ????DWORD????????????
#pragma pack(2) //2??????

// ??????????????
#define MAX_ITERATIONS 10000
// ?????N??????????????????????
#define MODEL_WIDTH 40   // ????????????
#define MODEL_HEIGHT 10  // ???????????
#define HALF_WIDTH (MODEL_WIDTH/2)
#define HALF_HEIGHT (MODEL_HEIGHT/2)
#define SAFE_DISTANCE 20  // ????????????

typedef struct BMP_FILE_HEADER
{
	WORD bfType;                    // ??????????????
	DWORD bfSize;                   // ??????????????????
	WORD bfReserved1;               // ?????,?????????0
	WORD bfReserved2;               // ?????,?????????0
	DWORD bfOffset;                 // ????????????????????????????
} BMPFILEHEADER;
typedef struct BMP_INFO_HEADER
{
	DWORD biInfoSize;               // ????????????
	DWORD biWidth;                  // ???????????????
	DWORD biHeight;                 // ??????????????
	WORD biPlanes;                  // ??????????????1
	WORD biBitCount;                // ??????????????????????1??4??8??16??24??32
	DWORD biCompression;            // ???????
	DWORD biImageSize;              // ???????,??????????
	DWORD biXPelsPerMeter;          // ???????
	DWORD biYPelsPerMeter;          // ????????
	DWORD biClrUsed;                // ?????????
	DWORD biClrImportant;           // ??????????
} BMPINFOHADER;
typedef struct
{
	short X;
	short Y;
} AStarPoint ;
typedef struct
{
	int IsRun;
	int G;
	int H;
	int F;
	AStarPoint ParentNode;
} AStarNode;
typedef struct
{
	int F;
	int index;
} AStarHeapNode;
typedef struct {
	short  MapWidth;
	short  MapHeight;
	unsigned char* MapData;
	AStarNode* MapNode;
	AStarHeapNode* OpenList;
	int OpenListCount;
	int code;
	char* memoryBlock;  // ?????????????????
	int* OpenListIndexMap;  // ???????????????????????????????????????

	// Distance Transform Optimization
	unsigned short* distanceMap;  // Distance transform map for fast wall distance queries
	int distanceMapValid;         // Flag indicating if distance map is valid
	AStarPoint* wallPoints;       // Array of wall positions for fast distance calculation
	int wallPointCount;           // Number of wall points
	int maxWallPoints;            // Maximum capacity of wallPoints array

	// GetMinWallDistance Optimization Cache
	struct {
		AStarPoint lastPoint;     // Last queried point
		int lastDistance;         // Last calculated distance
		int cacheValid;           // Cache validity flag
		int hitCount;             // Cache hit statistics
		int missCount;            // Cache miss statistics
	} wallDistanceCache;
} AStar;


#define ASTAR_API extern  __declspec(dllexport) 

ASTAR_API AStar* AStar_New(char* mapdata, int len, int code);
ASTAR_API void AStar_Free(AStar* As);
ASTAR_API void AStar_SaveMapFile(AStar* As,const char* path);
ASTAR_API int AStar_GetCode(AStar* As);
ASTAR_API char AStar_GetPointVal(AStar* As, AStarPoint Point);
ASTAR_API void AStar_SetPointVal(AStar* As, AStarPoint Point, char val);
ASTAR_API void AStar_SaveBMP(AStar* As, const char* path);
ASTAR_API int AStar_CreateMapFile(int MapWidth, int MapHeight, AStarPoint* losePoint, int losePointlen, const char* path);
ASTAR_API AStar* AStar_CreateMapFileInBMP(const unsigned char* bmpData, int bmpDataLen, const char* path, int code);
//0 ok -1 起点终点相同 -2 起点超出地图 -3 终点超出地图
//-4 终点是墙体 -5 起点是墙体 -6 内存分配失败 -7 无法找到路径 -8 部分路径可用(寻路失败但保留最接近路径)
ASTAR_API int AStar_FindPath(AStar* As, AStarPoint Start, AStarPoint End,int dir, AStarPoint* losePoint,int losePointlen, int LoseStartErr);
ASTAR_API AStarPoint AStar_GetPrevPoint(AStar* As, AStarPoint Point);
ASTAR_API AStarPoint AStar_GetClosestReachablePoint(AStar* As);

int AStar_LoadMapData(AStar* AP, char* mapdata, int len);
char* AStar_EMapToCMap(char* mapdata, int len, int* retLen);
void AStar_AddHeapNode(AStar* As, int index, int F);
void AStar_DelHeapNode(AStar* As);
void AStar_ComparisonHeapNode(AStar* As, int index);
int IsLineClear(AStar* As, AStarPoint start, AStarPoint end);

// ??????G?????????????????????
int CalculateNodeGCost(AStar* As, int baseG, AStarPoint End, AStarPoint FPoint, AStarPoint ZPoint,
                       int dirIndex, int has_prev, int prev_dx, int prev_dy);




#endif // ! ASTAR_H


