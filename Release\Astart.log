﻿  AStar.c
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(1,1): warning C4103: 包括标题后更改了对齐方式，可能是由于缺少 #pragma pack(pop)
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(171,23): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(331,23): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(386,17): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(406,23): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(408,24): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(460,23): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(462,24): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(496,25): warning C4013: “getMapIndex”未定义；假设外部返回 int
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(513,24): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(523,25): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(532,23): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(540,30): warning C4013: “calcH”未定义；假设外部返回 int
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(567,24): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(575,64): warning C4013: “CheckModelCollisionSmart”未定义；假设外部返回 int
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(595,27): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(651,26): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(663,24): warning C4018: “<”: 有符号/无符号不匹配
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(678,39): warning C4244: “函数”: 从“double”转换到“int”，可能丢失数据
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(678,72): warning C4244: “函数”: 从“double”转换到“int”，可能丢失数据
F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\AStar.c(810,42): warning C4013: “GetMinWallDistance_Sampled”未定义；假设外部返回 int
    正在创建库 F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\Release\Asatr.lib 和对象 F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\Release\Asatr.exp
  正在生成代码
  2 of 29 functions ( 6.9%) were compiled, the rest were copied from previous compilation.
    1 functions were new in current compilation
    0 functions had inline decision re-evaluated but remain unchanged
  已完成代码的生成
  Astart.vcxproj -> F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\Release\Asatr.dll
  F:\A_游戏源码\杂乱写法\a星\Astart\Astart_T - 副本\Release\Asatr.dll
  复制了 1 个文件
